import os
import sys
import json
import shutil
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

from multimodal_dataset import MultiModalDataset, create_data_splits, set_seed
from gcn_model import create_multimodal_model, GCN
from resnet3D_model import resnet3d
from train_multimodal_model import MultiModalDataLoader, evaluate_model, plot_confusion_matrix
from monai.transforms import (
    Compose, ScaleIntensity, RandRotate, RandFlip,
    RandZoom, Resize, ToTensor
)

def add_channel_dim(x):
    """为图像数据添加通道维度"""
    return x[None, ...]

def create_split_folders(dataset, train_indices, val_indices, test_indices):
    """
    创建训练集、验证集和测试集的分离文件夹
    """
    # 创建主文件夹
    split_folders = {
        'train': 'data_splits/train',
        'val': 'data_splits/val',
        'test': 'data_splits/test'
    }

    # 创建文件夹结构
    for split_name, folder_path in split_folders.items():
        for data_type in ['structural/HC', 'structural/MCI', 'functional/HC', 'functional/MCI']:
            os.makedirs(os.path.join(folder_path, data_type), exist_ok=True)

    # 复制文件到对应的分割文件夹
    indices_map = {
        'train': train_indices,
        'val': val_indices,
        'test': test_indices
    }

    for split_name, indices in indices_map.items():
        print(f"创建 {split_name} 数据文件夹...")
        for idx in indices:
            data_pair = dataset.data_pairs[idx]
            subject_id = data_pair['subject_id']
            label = data_pair['label']
            label_name = 'HC' if label == 0 else 'MCI'

            # 复制结构数据
            src_structural = data_pair['structural_path']
            dst_structural = os.path.join(split_folders[split_name], 'structural', label_name,
                                        os.path.basename(src_structural))
            if not os.path.exists(dst_structural):
                shutil.copy2(src_structural, dst_structural)

            # 复制功能数据
            src_timeseries = data_pair['timeseries_path']
            dst_timeseries = os.path.join(split_folders[split_name], 'functional', label_name,
                                        os.path.basename(src_timeseries))
            if not os.path.exists(dst_timeseries):
                shutil.copy2(src_timeseries, dst_timeseries)

            src_matrix = data_pair['matrix_path']
            dst_matrix = os.path.join(split_folders[split_name], 'functional', label_name,
                                    os.path.basename(src_matrix))
            if not os.path.exists(dst_matrix):
                shutil.copy2(src_matrix, dst_matrix)

    print("数据分割文件夹创建完成！")
    return split_folders

class StructuralOnlyDataLoader:
    """
    仅结构数据的数据加载器
    """
    def __init__(self, dataset, indices, batch_size=8, shuffle=False):
        self.dataset = dataset
        self.indices = indices
        self.batch_size = batch_size
        self.shuffle = shuffle
        
    def __iter__(self):
        if self.shuffle:
            indices = torch.randperm(len(self.indices)).tolist()
            batch_indices = [self.indices[i] for i in indices]
        else:
            batch_indices = self.indices
        
        for i in range(0, len(batch_indices), self.batch_size):
            batch_idx = batch_indices[i:i+self.batch_size]
            batch_data = []
            
            for idx in batch_idx:
                batch_data.append(self.dataset[idx])
            
            # 分离结构数据
            structural_data = torch.stack([item['structural_data'] for item in batch_data])
            labels = torch.tensor([item['label'] for item in batch_data], dtype=torch.long)
            subject_ids = [item['subject_id'] for item in batch_data]
            
            yield {
                'structural_data': structural_data,
                'labels': labels,
                'subject_ids': subject_ids
            }
    
    def __len__(self):
        return (len(self.indices) + self.batch_size - 1) // self.batch_size

class FunctionalOnlyDataLoader:
    """
    仅功能数据的数据加载器
    """
    def __init__(self, dataset, indices, batch_size=8, shuffle=False):
        self.dataset = dataset
        self.indices = indices
        self.batch_size = batch_size
        self.shuffle = shuffle
        
    def __iter__(self):
        if self.shuffle:
            indices = torch.randperm(len(self.indices)).tolist()
            batch_indices = [self.indices[i] for i in indices]
        else:
            batch_indices = self.indices
        
        for i in range(0, len(batch_indices), self.batch_size):
            batch_idx = batch_indices[i:i+self.batch_size]
            batch_data = []
            
            for idx in batch_idx:
                batch_data.append(self.dataset[idx])
            
            # 处理功能数据（图数据）
            functional_data_list = [item['functional_data'] for item in batch_data]
            labels = torch.tensor([item['label'] for item in batch_data], dtype=torch.long)
            subject_ids = [item['subject_id'] for item in batch_data]
            
            # 为图数据添加batch信息
            batch_tensor = []
            for i, data in enumerate(functional_data_list):
                batch_tensor.extend([i] * data.x.shape[0])
            
            # 合并所有图数据
            x_list = [data.x for data in functional_data_list]
            edge_index_list = [data.edge_index for data in functional_data_list]
            edge_attr_list = [data.edge_attr for data in functional_data_list]
            
            # 调整边索引以适应批处理
            node_offset = 0
            adjusted_edge_indices = []
            for edge_index in edge_index_list:
                adjusted_edge_indices.append(edge_index + node_offset)
                node_offset += x_list[len(adjusted_edge_indices)-1].shape[0]
            
            # 合并数据
            combined_x = torch.cat(x_list, dim=0)
            combined_edge_index = torch.cat(adjusted_edge_indices, dim=1)
            combined_edge_attr = torch.cat(edge_attr_list, dim=0)
            combined_batch = torch.tensor(batch_tensor, dtype=torch.long)
            
            # 创建功能数据对象
            from torch_geometric.data import Data
            functional_data = Data(
                x=combined_x,
                edge_index=combined_edge_index,
                edge_attr=combined_edge_attr,
                batch=combined_batch
            )
            
            yield {
                'functional_data': functional_data,
                'labels': labels,
                'subject_ids': subject_ids
            }
    
    def __len__(self):
        return (len(self.indices) + self.batch_size - 1) // self.batch_size

def train_structural_only_model(train_loader, val_loader, test_loader, config, device):
    """
    训练仅结构分支的模型
    """
    print("\n=== 训练仅结构分支模型 ===")
    
    # 创建结构模型
    model = resnet3d(
        model_name=config['structural_model'],
        in_channels=1,
        num_classes=2,
        init_weights=True,
        feature_extract=False
    ).to(device)
    
    # 加载预训练权重
    if config['pretrained_path'] and os.path.exists(config['pretrained_path']):
        try:
            checkpoint = torch.load(config['pretrained_path'], map_location=device)
            if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            else:
                state_dict = checkpoint
            model.load_state_dict(state_dict, strict=False)
            print(f"成功加载预训练权重: {config['pretrained_path']}")
        except Exception as e:
            print(f"加载预训练权重失败: {e}")
    
    # 优化器和损失函数
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), 
                          lr=config['learning_rate'], 
                          weight_decay=config['weight_decay'])
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=config['epochs'], eta_min=1e-6
    )
    
    # 训练循环
    best_val_acc = 0
    for epoch in range(config['epochs']):
        # 训练
        model.train()
        train_loss = 0
        correct = 0
        total = 0

        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{config['epochs']}"):
            structural_data = batch['structural_data'].to(device)
            labels = batch['labels'].to(device)

            optimizer.zero_grad()
            outputs = model(structural_data)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

        scheduler.step()

        # 验证
        model.eval()
        val_loss = 0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            for batch in val_loader:
                structural_data = batch['structural_data'].to(device)
                labels = batch['labels'].to(device)

                outputs = model(structural_data)
                loss = criterion(outputs, labels)

                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()

        val_acc = val_correct / val_total
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)

        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), 'best_structural_only_model.pth')

        # 打印每个epoch的结果，包括训练损失
        print(f"Epoch {epoch+1}: Train Loss: {avg_train_loss:.4f}, Train Acc: {correct/total:.4f}, Val Loss: {avg_val_loss:.4f}, Val Acc: {val_acc:.4f}")
    
    # 测试
    model.load_state_dict(torch.load('best_structural_only_model.pth'))
    model.eval()
    
    test_predictions = []
    test_labels = []
    test_subject_ids = []
    
    with torch.no_grad():
        for batch in test_loader:
            structural_data = batch['structural_data'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(structural_data)
            _, predicted = torch.max(outputs.data, 1)
            
            test_predictions.extend(predicted.cpu().numpy())
            test_labels.extend(labels.cpu().numpy())
            test_subject_ids.extend(batch['subject_ids'])
    
    test_acc = accuracy_score(test_labels, test_predictions)
    test_f1 = f1_score(test_labels, test_predictions, average='macro')
    
    print(f"仅结构分支训练完成，最佳验证准确率: {best_val_acc:.4f}")

    return {
        'accuracy': test_acc,
        'f1_score': test_f1,
        'predictions': test_predictions,
        'labels': test_labels,
        'subject_ids': test_subject_ids,
        'best_val_acc': best_val_acc
    }

def train_functional_only_model(train_loader, val_loader, test_loader, config, device):
    """
    训练仅功能分支的模型
    """
    print("\n=== 训练仅功能分支模型 ===")
    
    # 创建功能模型
    model = GCN(
        num_node_features=config['gcn_num_node_features'],
        hidden_channels=config['gcn_hidden_channels'],
        num_classes=2,
        feature_extract=False
    ).to(device)
    
    # 优化器和损失函数
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), 
                          lr=config['learning_rate'], 
                          weight_decay=config['weight_decay'])
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=config['epochs'], eta_min=1e-6
    )
    
    # 训练循环
    best_val_acc = 0
    for epoch in range(config['epochs']):
        # 训练
        model.train()
        train_loss = 0
        correct = 0
        total = 0

        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{config['epochs']}"):
            functional_data = batch['functional_data'].to(device)
            labels = batch['labels'].to(device)

            optimizer.zero_grad()
            outputs = model(functional_data.x, functional_data.edge_index, functional_data.batch)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

        scheduler.step()

        # 验证
        model.eval()
        val_loss = 0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            for batch in val_loader:
                functional_data = batch['functional_data'].to(device)
                labels = batch['labels'].to(device)

                outputs = model(functional_data.x, functional_data.edge_index, functional_data.batch)
                loss = criterion(outputs, labels)

                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()

        val_acc = val_correct / val_total
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)

        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), 'best_functional_only_model.pth')

        # 打印每个epoch的结果，包括训练损失
        print(f"Epoch {epoch+1}: Train Loss: {avg_train_loss:.4f}, Train Acc: {correct/total:.4f}, Val Loss: {avg_val_loss:.4f}, Val Acc: {val_acc:.4f}")
    
    # 测试
    model.load_state_dict(torch.load('best_functional_only_model.pth'))
    model.eval()
    
    test_predictions = []
    test_labels = []
    test_subject_ids = []
    
    with torch.no_grad():
        for batch in test_loader:
            functional_data = batch['functional_data'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(functional_data.x, functional_data.edge_index, functional_data.batch)
            _, predicted = torch.max(outputs.data, 1)
            
            test_predictions.extend(predicted.cpu().numpy())
            test_labels.extend(labels.cpu().numpy())
            test_subject_ids.extend(batch['subject_ids'])
    
    test_acc = accuracy_score(test_labels, test_predictions)
    test_f1 = f1_score(test_labels, test_predictions, average='macro')

    print(f"仅功能分支训练完成，最佳验证准确率: {best_val_acc:.4f}")

    return {
        'accuracy': test_acc,
        'f1_score': test_f1,
        'predictions': test_predictions,
        'labels': test_labels,
        'subject_ids': test_subject_ids,
        'best_val_acc': best_val_acc
    }

def run_ablation_study_with_config(config):
    """
    使用外部配置运行消融实验
    """
    # 设置随机种子
    set_seed(42)

    # 执行消融实验逻辑
    return _run_ablation_experiment(config)

def run_ablation_study():
    """
    运行完整的消融实验
    """
    # 设置随机种子
    set_seed(42)

    # 默认配置
    config = {
        'epochs': 50,
        'batch_size': 8,
        'learning_rate': 0.0001,
        'weight_decay': 0.01,
        'connectivity_threshold': 0.3
    }

    # 执行消融实验逻辑
    return _run_ablation_experiment(config)

def _run_ablation_experiment(config):
    """执行消融实验逻辑"""
    # 设备设置
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 补充默认配置参数（如果没有提供的话）
    default_config = {
        'batch_size': 8,
        'learning_rate': 0.0001,
        'weight_decay': 0.01,
        'epochs': 50,
        'structural_model': 'resnet34',
        'pretrained_path': 'resnet_18_23dataset.pth',
        'gcn_hidden_channels': 64,
        'gcn_num_node_features': 16,
        'fusion_dim': 256,
        'connectivity_threshold': 0.3,
        'dropout_rate': 0.5
    }

    # 合并配置，优先使用传入的配置
    for key, value in default_config.items():
        if key not in config:
            config[key] = value
    
    # 数据变换
    train_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        RandRotate(range_x=15, range_y=15, range_z=15, prob=0.5),
        RandFlip(prob=0.5, spatial_axis=0),
        RandZoom(min_zoom=0.9, max_zoom=1.1, prob=0.5),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    val_test_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    # 创建数据集
    print("创建数据集...")
    full_dataset = MultiModalDataset(
        structural_transform=None,
        connectivity_threshold=config['connectivity_threshold']
    )
    
    # 数据划分
    train_indices, val_indices, test_indices = create_data_splits(full_dataset)

    # 创建分离的数据文件夹
    print("创建训练集、验证集和测试集的分离文件夹...")
    split_folders = create_split_folders(full_dataset, train_indices, val_indices, test_indices)

    # 创建不同的数据集
    train_dataset = MultiModalDataset(
        structural_transform=train_transform,
        connectivity_threshold=config['connectivity_threshold']
    )
    val_test_dataset = MultiModalDataset(
        structural_transform=val_test_transform,
        connectivity_threshold=config['connectivity_threshold']
    )
    
    # 存储结果
    results = {}
    
    # 1. 仅结构分支实验
    print("\n开始仅结构分支实验...")
    structural_train_loader = StructuralOnlyDataLoader(
        train_dataset, train_indices, batch_size=config['batch_size'], shuffle=True
    )
    structural_val_loader = StructuralOnlyDataLoader(
        val_test_dataset, val_indices, batch_size=config['batch_size'], shuffle=False
    )
    structural_test_loader = StructuralOnlyDataLoader(
        val_test_dataset, test_indices, batch_size=config['batch_size'], shuffle=False
    )
    
    structural_results = train_structural_only_model(
        structural_train_loader, structural_val_loader, structural_test_loader, config, device
    )
    results['structural_only'] = structural_results
    
    # 2. 仅功能分支实验
    print("\n开始仅功能分支实验...")
    functional_train_loader = FunctionalOnlyDataLoader(
        train_dataset, train_indices, batch_size=config['batch_size'], shuffle=True
    )
    functional_val_loader = FunctionalOnlyDataLoader(
        val_test_dataset, val_indices, batch_size=config['batch_size'], shuffle=False
    )
    functional_test_loader = FunctionalOnlyDataLoader(
        val_test_dataset, test_indices, batch_size=config['batch_size'], shuffle=False
    )
    
    functional_results = train_functional_only_model(
        functional_train_loader, functional_val_loader, functional_test_loader, config, device
    )
    results['functional_only'] = functional_results
    
    # 3. 多模态融合模型（从之前的训练结果中获取）
    print("\n多模态融合模型结果将从主训练脚本中获取")
    
    # 打印消融实验结果
    print("\n=== 消融实验结果 ===")
    print(f"仅结构分支 - 测试准确率: {structural_results['accuracy']:.4f}, F1: {structural_results['f1_score']:.4f}, 最佳验证准确率: {structural_results['best_val_acc']:.4f}")
    print(f"仅功能分支 - 测试准确率: {functional_results['accuracy']:.4f}, F1: {functional_results['f1_score']:.4f}, 最佳验证准确率: {functional_results['best_val_acc']:.4f}")
    
    # 保存结果
    with open('ablation_study_results.json', 'w') as f:
        json.dump({
            'structural_only': {
                'accuracy': float(structural_results['accuracy']),
                'f1_score': float(structural_results['f1_score']),
                'best_val_acc': float(structural_results['best_val_acc'])
            },
            'functional_only': {
                'accuracy': float(functional_results['accuracy']),
                'f1_score': float(functional_results['f1_score']),
                'best_val_acc': float(functional_results['best_val_acc'])
            },
            'config': config
        }, f, indent=2)
    
    # 绘制对比图
    plot_ablation_comparison(results)
    
    return results

def plot_ablation_comparison(results):
    """
    绘制消融实验对比图
    """
    models = ['Structural Only', 'Functional Only']
    accuracies = [results['structural_only']['accuracy'], results['functional_only']['accuracy']]
    f1_scores = [results['structural_only']['f1_score'], results['functional_only']['f1_score']]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 准确率对比
    bars1 = ax1.bar(models, accuracies, color=['blue', 'red'], alpha=0.7)
    ax1.set_ylabel('Accuracy')
    ax1.set_title('Model Accuracy Comparison')
    ax1.set_ylim(0, 1)
    
    # 添加数值标签
    for bar, acc in zip(bars1, accuracies):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{acc:.3f}', ha='center', va='bottom')
    
    # F1分数对比
    bars2 = ax2.bar(models, f1_scores, color=['blue', 'red'], alpha=0.7)
    ax2.set_ylabel('F1 Score')
    ax2.set_title('Model F1 Score Comparison')
    ax2.set_ylim(0, 1)
    
    # 添加数值标签
    for bar, f1 in zip(bars2, f1_scores):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{f1:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('ablation_study_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    run_ablation_study()
