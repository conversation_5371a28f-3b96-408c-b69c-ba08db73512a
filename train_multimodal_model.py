import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
from torch_geometric.data import DataLoader as GeometricDataLoader
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

from multimodal_dataset import MultiModalDataset, create_data_splits, set_seed
from gcn_model import create_multimodal_model
from monai.transforms import (
    Compose, ScaleIntensity, RandRotate, RandFlip,
    RandZoom, Resize, ToTensor
)

def add_channel_dim(x):
    """为图像数据添加通道维度"""
    return x[None, ...]

class MultiModalDataLoader:
    """
    多模态数据加载器，处理结构数据和功能数据的同步加载
    """
    def __init__(self, dataset, indices, batch_size=8, shuffle=False):
        self.dataset = dataset
        self.indices = indices
        self.batch_size = batch_size
        self.shuffle = shuffle
        
    def __iter__(self):
        if self.shuffle:
            indices = torch.randperm(len(self.indices)).tolist()
            batch_indices = [self.indices[i] for i in indices]
        else:
            batch_indices = self.indices
        
        for i in range(0, len(batch_indices), self.batch_size):
            batch_idx = batch_indices[i:i+self.batch_size]
            batch_data = []
            
            for idx in batch_idx:
                batch_data.append(self.dataset[idx])
            
            # 分离结构数据和功能数据
            structural_data = torch.stack([item['structural_data'] for item in batch_data])
            labels = torch.tensor([item['label'] for item in batch_data], dtype=torch.long)
            subject_ids = [item['subject_id'] for item in batch_data]
            
            # 处理功能数据（图数据）
            functional_data_list = [item['functional_data'] for item in batch_data]
            
            # 为图数据添加batch信息
            batch_tensor = []
            for i, data in enumerate(functional_data_list):
                batch_tensor.extend([i] * data.x.shape[0])
            
            # 合并所有图数据
            x_list = [data.x for data in functional_data_list]
            edge_index_list = [data.edge_index for data in functional_data_list]
            edge_attr_list = [data.edge_attr for data in functional_data_list]
            
            # 调整边索引以适应批处理
            node_offset = 0
            adjusted_edge_indices = []
            for edge_index in edge_index_list:
                adjusted_edge_indices.append(edge_index + node_offset)
                node_offset += x_list[len(adjusted_edge_indices)-1].shape[0]
            
            # 合并数据
            combined_x = torch.cat(x_list, dim=0)
            combined_edge_index = torch.cat(adjusted_edge_indices, dim=1)
            combined_edge_attr = torch.cat(edge_attr_list, dim=0)
            combined_batch = torch.tensor(batch_tensor, dtype=torch.long)
            
            # 创建功能数据对象
            from torch_geometric.data import Data
            functional_data = Data(
                x=combined_x,
                edge_index=combined_edge_index,
                edge_attr=combined_edge_attr,
                batch=combined_batch
            )
            
            yield {
                'structural_data': structural_data,
                'functional_data': functional_data,
                'labels': labels,
                'subject_ids': subject_ids
            }
    
    def __len__(self):
        return (len(self.indices) + self.batch_size - 1) // self.batch_size

def train_epoch(model, train_loader, optimizer, criterion, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    progress_bar = tqdm(train_loader, desc="Training")
    
    for batch in progress_bar:
        structural_data = batch['structural_data'].to(device)
        functional_data = batch['functional_data'].to(device)
        labels = batch['labels'].to(device)
        
        optimizer.zero_grad()
        
        # 前向传播
        outputs = model(structural_data, functional_data)
        loss = criterion(outputs, labels)
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        _, predicted = torch.max(outputs.data, 1)
        total += labels.size(0)
        correct += (predicted == labels).sum().item()
        
        # 更新进度条
        progress_bar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })
    
    return total_loss / len(train_loader), correct / total

def evaluate_model(model, data_loader, criterion, device):
    """评估模型"""
    model.eval()
    total_loss = 0
    all_predictions = []
    all_labels = []
    all_subject_ids = []
    
    with torch.no_grad():
        for batch in tqdm(data_loader, desc="Evaluating"):
            structural_data = batch['structural_data'].to(device)
            functional_data = batch['functional_data'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(structural_data, functional_data)
            loss = criterion(outputs, labels)
            
            total_loss += loss.item()
            
            _, predicted = torch.max(outputs.data, 1)
            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_subject_ids.extend(batch['subject_ids'])
    
    # 计算指标
    accuracy = accuracy_score(all_labels, all_predictions)
    f1 = f1_score(all_labels, all_predictions, average='macro')
    
    return {
        'loss': total_loss / len(data_loader),
        'accuracy': accuracy,
        'f1_score': f1,
        'predictions': all_predictions,
        'labels': all_labels,
        'subject_ids': all_subject_ids
    }

def plot_confusion_matrix(y_true, y_pred, save_path=None):
    """绘制混淆矩阵"""
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['HC', 'MCI'],
                yticklabels=['HC', 'MCI'])
    plt.xlabel('Predicted Labels')
    plt.ylabel('True Labels')
    plt.title('Confusion Matrix')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

def save_results(results, save_path):
    """保存结果"""
    # 保存详细结果
    detailed_results = {
        'subject_results': [
            {
                'subject_id': sid,
                'true_label': int(true_label),
                'predicted_label': int(pred_label)
            }
            for sid, true_label, pred_label in zip(
                results['subject_ids'], 
                results['labels'], 
                results['predictions']
            )
        ],
        'overall_metrics': {
            'accuracy': float(results['accuracy']),
            'f1_score': float(results['f1_score']),
            'loss': float(results['loss'])
        }
    }
    
    with open(save_path, 'w') as f:
        json.dump(detailed_results, f, indent=2)

def main_with_config(config):
    """使用外部配置运行训练"""
    # 设置随机种子
    set_seed(42)

    # 设备设置
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 打印配置参数
    print("配置参数:")
    for key, value in config.items():
        print(f"  {key}: {value}")

    # 执行训练逻辑
    _run_training(config, device)

def main():
    # 设置随机种子
    set_seed(42)

    # 设备设置
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 超参数设置
    config = {
        'batch_size': 8,
        'learning_rate': 0.0001,
        'weight_decay': 0.01,
        'epochs': 100,
        'structural_model': 'resnet34',
        'pretrained_path': 'resnet_18_23dataset.pth',
        'gcn_hidden_channels': 64,
        'gcn_num_node_features': 16,
        'fusion_dim': 256,
        'connectivity_threshold': 0.3,
        'dropout_rate': 0.5
    }

    print("配置参数:")
    for key, value in config.items():
        print(f"  {key}: {value}")

    # 执行训练逻辑
    _run_training(config, device)

def _run_training(config, device):
    """执行训练逻辑"""
    # 数据变换
    train_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        RandRotate(range_x=15, range_y=15, range_z=15, prob=0.5),
        RandFlip(prob=0.5, spatial_axis=0),
        RandZoom(min_zoom=0.9, max_zoom=1.1, prob=0.5),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    val_test_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    # 创建数据集
    print("创建数据集...")
    full_dataset = MultiModalDataset(
        structural_transform=None,  # 稍后设置
        connectivity_threshold=config['connectivity_threshold']
    )
    
    # 数据划分
    train_indices, val_indices, test_indices = create_data_splits(full_dataset)
    
    # 创建数据加载器
    print("创建数据加载器...")
    
    # 为不同的数据集设置不同的变换
    train_dataset = MultiModalDataset(
        structural_transform=train_transform,
        connectivity_threshold=config['connectivity_threshold']
    )
    val_test_dataset = MultiModalDataset(
        structural_transform=val_test_transform,
        connectivity_threshold=config['connectivity_threshold']
    )
    
    train_loader = MultiModalDataLoader(
        train_dataset, train_indices, 
        batch_size=config['batch_size'], shuffle=True
    )
    val_loader = MultiModalDataLoader(
        val_test_dataset, val_indices,
        batch_size=config['batch_size'], shuffle=False
    )
    test_loader = MultiModalDataLoader(
        val_test_dataset, test_indices,
        batch_size=config['batch_size'], shuffle=False
    )
    
    # 创建模型
    print("创建多模态融合模型...")
    model = create_multimodal_model(
        structural_model_name=config['structural_model'],
        structural_pretrained_path=config['pretrained_path'],
        gcn_hidden_channels=config['gcn_hidden_channels'],
        gcn_num_node_features=config['gcn_num_node_features'],
        fusion_dim=config['fusion_dim'],
        num_classes=2,
        device=device
    ).to(device)
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型总参数数: {total_params:,}")
    print(f"可训练参数数: {trainable_params:,}")
    
    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), 
                          lr=config['learning_rate'], 
                          weight_decay=config['weight_decay'])
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=config['epochs'], eta_min=1e-6
    )
    
    # 训练循环
    print("开始训练...")
    best_val_acc = 0
    train_losses = []
    train_accs = []
    val_losses = []
    val_accs = []
    
    for epoch in range(config['epochs']):
        print(f"\nEpoch {epoch+1}/{config['epochs']}")
        
        # 训练
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)
        
        # 验证
        val_results = evaluate_model(model, val_loader, criterion, device)
        
        # 更新学习率
        scheduler.step()
        
        # 记录指标
        train_losses.append(train_loss)
        train_accs.append(train_acc)
        val_losses.append(val_results['loss'])
        val_accs.append(val_results['accuracy'])
        
        # 打印结果
        current_lr = optimizer.param_groups[0]['lr']
        print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
        print(f"Val Loss: {val_results['loss']:.4f}, Val Acc: {val_results['accuracy']:.4f}, Val F1: {val_results['f1_score']:.4f}")
        print(f"Learning Rate: {current_lr:.6f}")
        
        # 保存最佳模型
        if val_results['accuracy'] > best_val_acc:
            best_val_acc = val_results['accuracy']
            torch.save(model.state_dict(), 'best_multimodal_model.pth')
            print(f"保存最佳模型，验证准确率: {best_val_acc:.4f}")
    
    # 加载最佳模型进行测试
    print("\n加载最佳模型进行测试...")
    model.load_state_dict(torch.load('best_multimodal_model.pth'))

    # 测试
    test_results = evaluate_model(model, test_loader, criterion, device)

    print(f"\n=== 多模态训练完成 ===")
    print(f"最佳验证准确率: {best_val_acc:.4f}")
    print(f"\n=== 最终测试结果 ===")
    print(f"测试准确率: {test_results['accuracy']:.4f}")
    print(f"测试F1分数: {test_results['f1_score']:.4f}")
    print(f"测试损失: {test_results['loss']:.4f}")
    
    # 详细分类报告
    print("\n分类报告:")
    print(classification_report(test_results['labels'], test_results['predictions'],
                              target_names=['HC', 'MCI']))
    
    # 绘制混淆矩阵
    plot_confusion_matrix(test_results['labels'], test_results['predictions'],
                         'test_confusion_matrix.png')
    
    # 保存结果
    save_results(test_results, 'test_results.json')
    
    # 可解释性分析
    print("\n进行可解释性分析...")
    from interpretability_analysis import analyze_model_interpretability

    try:
        analyze_model_interpretability(model, test_loader, device, save_dir='interpretability_results')
        print("可解释性分析完成，结果保存在 interpretability_results/ 目录")
    except Exception as e:
        print(f"可解释性分析失败: {e}")

    # 保存训练历史
    training_history = {
        'train_losses': train_losses,
        'train_accs': train_accs,
        'val_losses': val_losses,
        'val_accs': val_accs,
        'config': config,
        'best_val_acc': best_val_acc,
        'test_results': {
            'accuracy': test_results['accuracy'],
            'f1_score': test_results['f1_score'],
            'loss': test_results['loss']
        }
    }

    with open('training_history.json', 'w') as f:
        json.dump(training_history, f, indent=2)

    print("\n训练完成！结果已保存。")

if __name__ == "__main__":
    main()
